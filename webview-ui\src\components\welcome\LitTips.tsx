import { VSCodeLink } from "@vscode/webview-ui-toolkit/react"
import { useTranslation } from "react-i18next"
import { Trans } from "react-i18next"

import { buildDocLink } from "@src/utils/docLinks"

const tips = [
	{
		icon: "codicon-account",
		href: buildDocLink("basic-usage/using-modes", "tips"),
		titleKey: "litTips.customizableModes.title",
		descriptionKey: "litTips.customizableModes.description",
	},
	{
		icon: "codicon-list-tree",
		href: buildDocLink("features/boomerang-tasks", "tips"),
		titleKey: "litTips.boomerangTasks.title",
		descriptionKey: "litTips.boomerangTasks.description",
	},
]

const LitTips = () => {
	const { t } = useTranslation("chat")

	return (
		<div className="space-y-6">
			<p className="text-vscode-editor-foreground leading-relaxed text-center text-balance max-w-md mx-auto text-base">
				<Trans
					i18nKey="chat:about"
					components={{
						DocsLink: (
							<a
								href={buildDocLink("", "welcome")}
								target="_blank"
								rel="noopener noreferrer"
								className="text-vscode-textLink-foreground hover:text-vscode-textLink-activeForeground underline transition-colors duration-200"
							>
								the docs
							</a>
						),
					}}
				/>
			</p>
			<div className="flex flex-col items-center justify-center gap-4 max-w-lg mx-auto">
				{tips.map((tip) => (
					<div
						key={tip.titleKey}
						className="group flex items-start gap-4 p-4 rounded-lg border border-vscode-panel-border bg-vscode-editor-background hover:bg-vscode-list-hoverBackground transition-all duration-200 w-full">
						<div className="flex-shrink-0 w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center">
							<span className={`codicon ${tip.icon} text-vscode-foreground`}></span>
						</div>
						<div className="flex-1 min-w-0">
							<VSCodeLink
								className="forced-color-adjust-none font-medium text-vscode-foreground group-hover:text-vscode-textLink-foreground transition-colors duration-200"
								href={tip.href}
							>
								{t(tip.titleKey)}
							</VSCodeLink>
							<p className="text-sm text-vscode-descriptionForeground mt-1 leading-relaxed">
								{t(tip.descriptionKey)}
							</p>
						</div>
					</div>
				))}
			</div>
		</div>
	)
}

export default LitTips
