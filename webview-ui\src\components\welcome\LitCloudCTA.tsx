import { useTranslation } from "react-i18next"

export function LitCloudCTA() {
	const { t } = useTranslation("chat")

	return (
		<div className="relative overflow-hidden rounded-xl border border-vscode-panel-border bg-gradient-to-br from-vscode-editor-background to-vscode-sideBar-background p-6 shadow-lg">
			{/* Background decoration */}
			<div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5" />
			<div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-blue-400/10 to-transparent rounded-full -translate-y-16 translate-x-16" />

			<div className="relative flex items-start gap-4">
				<div className="flex-shrink-0">
					<div className="w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-lg">
						<i className="codicon codicon-cloud text-white text-xl" />
					</div>
				</div>

				<div className="flex-1 min-w-0">
					<h3 className="text-lg font-semibold text-vscode-foreground mb-2 leading-tight">
						{t("litCloudCTA.title")}
					</h3>
					<p className="text-sm text-vscode-descriptionForeground mb-4 leading-relaxed">
						{t("litCloudCTA.description")}
					</p>
					<a
						href="https://litcode.com/cloud-waitlist"
						className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-sm font-medium rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
					>
						{t("litCloudCTA.joinWaitlist")}
						<i className="codicon codicon-arrow-right text-xs" />
					</a>
				</div>
			</div>
		</div>
	)
}

export default LitCloudCTA
