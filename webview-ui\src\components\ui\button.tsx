import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
	"btn-base rounded-md text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-vscode-focusBorder focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
	{
		variants: {
			variant: {
				default:
					"bg-vscode-button-background text-vscode-button-foreground border border-vscode-input-border hover:bg-vscode-button-hoverBackground shadow-soft",
				destructive:
					"bg-error text-error-foreground hover:bg-error/90 shadow-soft",
				outline:
					"border border-vscode-input-border bg-transparent hover:bg-vscode-list-hoverBackground hover:text-vscode-list-hoverForeground shadow-subtle",
				secondary:
					"bg-vscode-button-secondaryBackground text-vscode-button-secondaryForeground border border-vscode-input-border hover:bg-vscode-button-secondaryBackground/80 shadow-soft",
				ghost:
					"hover:bg-vscode-list-hoverBackground hover:text-vscode-list-hoverForeground",
				link:
					"text-vscode-textLink-foreground underline-offset-4 hover:underline p-0 h-auto",
				success:
					"bg-success text-success-foreground hover:bg-success/90 shadow-soft",
				warning:
					"bg-warning text-warning-foreground hover:bg-warning/90 shadow-soft",
				info:
					"bg-info text-info-foreground hover:bg-info/90 shadow-soft",
				combobox:
					"border border-vscode-dropdown-border focus-visible:border-vscode-focusBorder bg-vscode-dropdown-background hover:bg-vscode-list-hoverBackground text-vscode-dropdown-foreground font-normal shadow-subtle",
			},
			size: {
				xs: "h-6 px-2 text-xs rounded-sm",
				sm: "h-7 px-3 text-sm rounded-sm",
				default: "h-8 px-4 text-sm",
				lg: "h-9 px-6 text-base",
				xl: "h-10 px-8 text-lg",
				icon: "h-8 w-8",
				"icon-sm": "h-7 w-7",
				"icon-lg": "h-9 w-9",
				"icon-xl": "h-10 w-10",
			},
		},
		defaultVariants: {
			variant: "default",
			size: "default",
		},
	},
)

export interface ButtonProps
	extends React.ButtonHTMLAttributes<HTMLButtonElement>,
		VariantProps<typeof buttonVariants> {
	asChild?: boolean
	loading?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
	({ className, variant, size, asChild = false, loading = false, disabled, children, ...props }, ref) => {
		const Comp = asChild ? Slot : "button"
		const isDisabled = disabled || loading

		return (
			<Comp
				className={cn(buttonVariants({ variant, size, className }))}
				ref={ref}
				disabled={isDisabled}
				aria-disabled={isDisabled}
				{...props}
			>
				{loading && (
					<svg
						className="mr-2 h-4 w-4 animate-spin"
						xmlns="http://www.w3.org/2000/svg"
						fill="none"
						viewBox="0 0 24 24"
						aria-hidden="true"
					>
						<circle
							className="opacity-25"
							cx="12"
							cy="12"
							r="10"
							stroke="currentColor"
							strokeWidth="4"
						/>
						<path
							className="opacity-75"
							fill="currentColor"
							d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
						/>
					</svg>
				)}
				{children}
			</Comp>
		)
	},
)
Button.displayName = "Button"

export { Button, buttonVariants }
