import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs))
}

/**
 * Design System Utilities
 */

/**
 * Creates a focus ring class with VSCode theme integration
 */
export function focusRing(variant: "default" | "inset" = "default") {
	return variant === "inset" ? "focus-ring-inset" : "focus-ring"
}

/**
 * Creates interactive state classes for hover and active states
 */
export function interactive(enabled = true) {
	return enabled ? "interactive" : ""
}

/**
 * Creates shadow classes based on elevation level
 */
export function shadow(level: "subtle" | "soft" | "medium" | "elevated" | "high" = "soft") {
	const shadows = {
		subtle: "shadow-subtle",
		soft: "shadow-soft",
		medium: "shadow-medium",
		elevated: "shadow-elevated",
		high: "shadow-high",
	}
	return shadows[level]
}

/**
 * Creates state-based color classes
 */
export function stateColor(state: "success" | "warning" | "error" | "info") {
	const states = {
		success: "state-success",
		warning: "state-warning",
		error: "state-error",
		info: "state-info",
	}
	return states[state]
}

/**
 * Creates size-based classes
 */
export function sizeVariant(size: "xs" | "sm" | "md" | "lg" | "xl") {
	const sizes = {
		xs: "size-xs",
		sm: "size-sm",
		md: "size-md",
		lg: "size-lg",
		xl: "size-xl",
	}
	return sizes[size]
}

/**
 * Creates transition classes with different durations
 */
export function transition(
	type: "all" | "colors" = "colors",
	duration: "fast" | "normal" | "slow" | "slower" = "normal"
) {
	const durations = {
		fast: "duration-150",
		normal: "duration-200",
		slow: "duration-300",
		slower: "duration-500",
	}
	
	const base = type === "all" ? "transition-all" : "transition-colors"
	return cn(base, durations[duration])
}

/**
 * Creates responsive text size classes
 */
export function textSize(size: "2xs" | "xs" | "sm" | "base" | "lg" | "xl" | "2xl" | "3xl") {
	const sizes = {
		"2xs": "text-2xs",
		xs: "text-xs",
		sm: "text-sm",
		base: "text-base",
		lg: "text-lg",
		xl: "text-xl",
		"2xl": "text-2xl",
		"3xl": "text-3xl",
	}
	return sizes[size]
}

/**
 * Creates spacing classes
 */
export function spacing(size: "xs" | "sm" | "md" | "lg" | "xl" | "2xl" | "3xl") {
	return `p-${size}`
}

/**
 * Creates border radius classes
 */
export function borderRadius(size: "xs" | "sm" | "md" | "lg" | "xl" | "2xl" | "full") {
	const radii = {
		xs: "rounded-xs",
		sm: "rounded-sm",
		md: "rounded-md",
		lg: "rounded-lg",
		xl: "rounded-xl",
		"2xl": "rounded-2xl",
		full: "rounded-full",
	}
	return radii[size]
}

/**
 * Utility to conditionally apply classes based on state
 */
export function conditionalClass(condition: boolean, trueClass: string, falseClass = "") {
	return condition ? trueClass : falseClass
}

/**
 * Creates VSCode theme-aware color classes
 */
export function vscodeColor(
	color:
		| "foreground"
		| "background"
		| "button-background"
		| "button-foreground"
		| "input-background"
		| "input-foreground"
		| "badge-background"
		| "badge-foreground"
		| "error"
		| "focus-border"
		| "description"
) {
	const colors = {
		foreground: "text-vscode-foreground",
		background: "bg-vscode-editor-background",
		"button-background": "bg-vscode-button-background",
		"button-foreground": "text-vscode-button-foreground",
		"input-background": "bg-vscode-input-background",
		"input-foreground": "text-vscode-input-foreground",
		"badge-background": "bg-vscode-badge-background",
		"badge-foreground": "text-vscode-badge-foreground",
		error: "text-vscode-errorForeground",
		"focus-border": "border-vscode-focusBorder",
		description: "text-vscode-descriptionForeground",
	}
	return colors[color]
}

/**
 * Type-safe variant creator for components
 */
export function createVariants<T extends Record<string, any>>(variants: T): T {
	return variants
}
