import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const inputVariants = cva(
	"input-base w-full text-vscode-input-foreground border bg-vscode-input-background transition-colors file:border-0 file:bg-transparent file:font-medium placeholder:text-muted-foreground focus:outline-0 focus-visible:outline-none focus-visible:border-vscode-focusBorder disabled:cursor-not-allowed disabled:opacity-50",
	{
		variants: {
			variant: {
				default: "border-vscode-dropdown-border hover:border-vscode-input-border",
				outline: "border-vscode-input-border bg-transparent hover:bg-vscode-input-background/50",
				filled: "border-transparent bg-vscode-list-hoverBackground hover:bg-vscode-list-hoverBackground/80",
				ghost: "border-transparent bg-transparent hover:bg-vscode-list-hoverBackground/50",
			},
			size: {
				sm: "h-7 px-2 py-1 text-sm rounded-sm",
				default: "h-8 px-3 py-1.5 text-sm rounded-md",
				lg: "h-9 px-4 py-2 text-base rounded-md",
			},
			state: {
				default: "",
				error: "border-error focus-visible:border-error focus-visible:ring-error/20",
				success: "border-success focus-visible:border-success focus-visible:ring-success/20",
				warning: "border-warning focus-visible:border-warning focus-visible:ring-warning/20",
			},
		},
		defaultVariants: {
			variant: "default",
			size: "default",
			state: "default",
		},
	},
)

export interface InputProps
	extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "size">,
		VariantProps<typeof inputVariants> {
	/**
	 * Error message to display below the input
	 */
	error?: string
	/**
	 * Success message to display below the input
	 */
	success?: string
	/**
	 * Warning message to display below the input
	 */
	warning?: string
	/**
	 * Helper text to display below the input
	 */
	helperText?: string
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
	({ className, type, variant, size, state, error, success, warning, helperText, ...props }, ref) => {
		// Determine state based on props
		const inputState = error ? "error" : success ? "success" : warning ? "warning" : state

		const message = error || success || warning || helperText

		return (
			<div className="w-full">
				<input
					type={type}
					className={cn(inputVariants({ variant, size, state: inputState, className }))}
					ref={ref}
					aria-invalid={error ? "true" : undefined}
					aria-describedby={message ? `${props.id || "input"}-message` : undefined}
					{...props}
				/>
				{message && (
					<p
						id={`${props.id || "input"}-message`}
						className={cn(
							"mt-1 text-xs",
							error && "text-error",
							success && "text-success",
							warning && "text-warning",
							!error && !success && !warning && "text-vscode-descriptionForeground"
						)}
					>
						{message}
					</p>
				)}
			</div>
		)
	},
)
Input.displayName = "Input"

export { Input, inputVariants }
