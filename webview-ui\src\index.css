/**
 * Normally we'd import tailwind with the following:
 *
 * @import "tailwindcss";
 *
 * However, we need to customize the preflight styles since the extension's
 * current UI assumes there's no CSS resetting or normalization.
 *
 * We're excluding tailwind's default preflight and importing our own, which
 * is based on the original:
 * https://github.com/tailwindlabs/tailwindcss/blob/main/packages/tailwindcss/preflight.css
 *
 * Reference: https://tailwindcss.com/docs/preflight
 */

@layer theme, base, components, utilities;

@import "tailwindcss/theme.css" layer(theme);
@import "./preflight.css" layer(base);
@import "tailwindcss/utilities.css" layer(utilities);
@import "katex/dist/katex.min.css";

@plugin "tailwindcss-animate";

@theme {
	--font-display: var(--vscode-font-family);

	/* Enhanced Typography Scale */
	--text-2xs: calc(var(--vscode-font-size) * 0.75);
	--text-xs: calc(var(--vscode-font-size) * 0.85);
	--text-sm: calc(var(--vscode-font-size) * 0.9);
	--text-base: var(--vscode-font-size);
	--text-lg: calc(var(--vscode-font-size) * 1.1);
	--text-xl: calc(var(--vscode-font-size) * 1.25);
	--text-2xl: calc(var(--vscode-font-size) * 1.5);
	--text-3xl: calc(var(--vscode-font-size) * 1.875);

	/* Standardized Spacing Scale */
	--spacing-xs: 0.25rem;
	--spacing-sm: 0.5rem;
	--spacing-md: 0.75rem;
	--spacing-lg: 1rem;
	--spacing-xl: 1.5rem;
	--spacing-2xl: 2rem;
	--spacing-3xl: 3rem;

	/* Enhanced Border Radius Scale */
	--radius-xs: 0.125rem;
	--radius-sm: 0.25rem;
	--radius-md: 0.375rem;
	--radius-lg: 0.5rem;
	--radius-xl: 0.75rem;
	--radius-2xl: 1rem;
	--radius-full: 9999px;

	/* Animation and Transition Tokens */
	--duration-fast: 150ms;
	--duration-normal: 200ms;
	--duration-slow: 300ms;
	--duration-slower: 500ms;
	
	--ease-linear: linear;
	--ease-in: cubic-bezier(0.4, 0, 1, 1);
	--ease-out: cubic-bezier(0, 0, 0.2, 1);
	--ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

	/* Shadow Scale */
	--shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
	--shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
	--shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
	--shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
	--shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

	/* Base Color System */
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-destructive: var(--destructive);
	--color-destructive-foreground: var(--destructive-foreground);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);
	--color-chart-1: var(--chart-1);
	--color-chart-2: var(--chart-2);
	--color-chart-3: var(--chart-3);
	--color-chart-4: var(--chart-4);
	--color-chart-5: var(--chart-5);

	/* Semantic Color Tokens */
	--color-success: var(--vscode-charts-green, #22c55e);
	--color-success-foreground: var(--vscode-button-foreground, #ffffff);
	--color-warning: var(--vscode-charts-yellow, #f59e0b);
	--color-warning-foreground: var(--vscode-button-foreground, #ffffff);
	--color-error: var(--vscode-errorForeground, #ef4444);
	--color-error-foreground: var(--vscode-button-foreground, #ffffff);
	--color-info: var(--vscode-charts-blue, #3b82f6);
	--color-info-foreground: var(--vscode-button-foreground, #ffffff);

	/* Legacy radius tokens for backward compatibility */
	--radius-lg: var(--radius);
	--radius-md: calc(var(--radius) - 2px);
	--radius-sm: calc(var(--radius) - 4px);

	/**
	 * Allow VSCode colors to be used with Tailwind.
	 */

	--color-vscode-foreground: var(--vscode-foreground);

	--color-vscode-editor-foreground: var(--vscode-editor-foreground);
	--color-vscode-editor-background: var(--vscode-editor-background);

	--color-vscode-editorGroup-border: var(--vscode-editorGroup-border);

	--color-vscode-editorWarning-foreground: var(--vscode-editorWarning-foreground);
	--color-vscode-editorWarning-background: var(--vscode-editorWarning-background);

	--color-vscode-button-foreground: var(--vscode-button-foreground);
	--color-vscode-button-background: var(--vscode-button-background);
	--color-vscode-button-secondaryForeground: var(--vscode-button-secondaryForeground);
	--color-vscode-button-secondaryBackground: var(--vscode-button-secondaryBackground);

	--color-vscode-dropdown-foreground: var(--vscode-dropdown-foreground);
	--color-vscode-dropdown-background: var(--vscode-dropdown-background);
	--color-vscode-dropdown-border: var(--vscode-dropdown-border);

	--color-vscode-input-foreground: var(--vscode-input-foreground);
	--color-vscode-input-background: var(--vscode-input-background);
	--color-vscode-input-border: var(
		--vscode-input-border,
		transparent
	); /* Some themes don't have a border color, so we can fallback to transparent */

	--color-vscode-focusBorder: var(--vscode-focusBorder);

	--color-vscode-badge-foreground: var(--vscode-badge-foreground);
	--color-vscode-badge-background: var(--vscode-badge-background);

	--color-vscode-notifications-foreground: var(--vscode-notifications-foreground);
	--color-vscode-notifications-background: var(--vscode-notifications-background);
	--color-vscode-notifications-border: var(--vscode-notifications-border);

	--color-vscode-descriptionForeground: var(--vscode-descriptionForeground);
	--color-vscode-errorForeground: var(--vscode-errorForeground);

	--color-vscode-list-hoverForeground: var(--vscode-list-hoverForeground);
	--color-vscode-list-hoverBackground: var(--vscode-list-hoverBackground);
	--color-vscode-list-focusBackground: var(--vscode-list-focusBackground);
	--color-vscode-list-activeSelectionBackground: var(--vscode-list-activeSelectionBackground);
	--color-vscode-list-activeSelectionForeground: var(--vscode-list-activeSelectionForeground);

	--color-vscode-toolbar-hoverBackground: var(--vscode-toolbar-hoverBackground);

	--color-vscode-panel-border: var(--vscode-panel-border);

	--color-vscode-sideBar-foreground: var(--vscode-sideBar-foreground);
	--color-vscode-sideBar-background: var(--vscode-sideBar-background);
	--color-vscode-sideBar-border: var(--vscode-sideBar-border);

	--color-vscode-sideBarSectionHeader-foreground: var(--vscode-sideBarSectionHeader-foreground);
	--color-vscode-sideBarSectionHeader-background: var(--vscode-sideBarSectionHeader-background);
	--color-vscode-sideBarSectionHeader-border: var(--vscode-sideBarSectionHeader-border);

	--color-vscode-titleBar-activeForeground: var(--vscode-titleBar-activeForeground);
	--color-vscode-titleBar-inactiveForeground: var(--vscode-titleBar-inactiveForeground);

	--color-vscode-charts-green: var(--vscode-charts-green);
	--color-vscode-charts-yellow: var(--vscode-charts-yellow);

	--color-vscode-inputValidation-infoForeground: var(--vscode-inputValidation-infoForeground);
	--color-vscode-inputValidation-infoBackground: var(--vscode-inputValidation-infoBackground);
	--color-vscode-inputValidation-infoBorder: var(--vscode-inputValidation-infoBorder);

	--color-vscode-widget-border: var(--vscode-widget-border);
	--color-vscode-textLink-foreground: var(--vscode-textLink-foreground);
	--color-vscode-textCodeBlock-background: var(--vscode-textCodeBlock-background);
	--color-vscode-button-hoverBackground: var(--vscode-button-hoverBackground);
}

@layer base {
	:root {
		--background: var(--vscode-editor-background);
		--foreground: var(--vscode-editor-foreground);
		--card: var(--vscode-editor-background);
		--card-foreground: var(--vscode-editor-foreground);
		--popover: var(--vscode-menu-background, var(--vscode-editor-background));
		--popover-foreground: var(--vscode-menu-foreground, var(--vscode-editor-foreground));
		--primary: var(--vscode-button-background);
		--primary-foreground: var(--vscode-button-foreground);
		--secondary: var(--vscode-button-secondaryBackground);
		--secondary-foreground: var(--vscode-button-secondaryForeground);
		--muted: var(--vscode-disabledForeground);
		--muted-foreground: var(--vscode-descriptionForeground);
		--accent: var(--vscode-list-hoverBackground);
		--accent-foreground: var(--vscode-list-hoverForeground);
		--destructive: var(--vscode-errorForeground);
		--destructive-foreground: var(--vscode-button-foreground);
		--border: var(--vscode-input-border, transparent); /* --border gets theme value or transparent fallback */
		--input: var(--vscode-input-background);
		--ring: var(--vscode-input-border);
		--chart-1: var(--vscode-charts-red);
		--chart-2: var(--vscode-charts-blue);
		--chart-3: var(--vscode-charts-yellow);
		--chart-4: var(--vscode-charts-orange);
		--chart-5: var(--vscode-charts-green);
		--radius: 0.5rem;
	}

	/* Higher specififty than VSCode's theme and root. */
	/* Used for baseline theme overrides, but avoid using for styling. */

	body {
		--vscode-input-border: var(--border);
	}
}

@layer components {
	/* Border Styles */
	.border,
	.border-r,
	.border-l,
	.border-t,
	.border-b,
	.border-x,
	.border-y {
		border-color: var(--border);
	}

	/* Code Block Styles */
	pre,
	code {
		background-color: var(--vscode-textCodeBlock-background);
	}

	/* Search result highlighting */
	.history-item-highlight {
		@apply underline;
	}

	/* Enhanced Focus States for Accessibility */
	.focus-ring {
		@apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-vscode-focusBorder focus-visible:ring-offset-1;
	}

	.focus-ring-inset {
		@apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-inset focus-visible:ring-vscode-focusBorder;
	}

	/* Enhanced Interactive States */
	.interactive {
		@apply transition-colors duration-150 ease-out;
	}

	/* Modern Card Styles */
	.modern-card {
		background-color: var(--vscode-editor-background);
		border: 1px solid var(--vscode-panel-border);
		border-radius: 0.75rem;
		box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
		transition: all 0.2s ease;
	}

	.modern-card:hover {
		box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
	}

	.modern-card-hover:hover {
		border-color: var(--vscode-focusBorder);
		background-color: var(--vscode-list-hoverBackground);
		transform: translateY(-2px);
	}

	/* Gradient Backgrounds */
	.gradient-bg-primary {
		background: linear-gradient(135deg, rgb(59 130 246 / 0.1) 0%, rgb(147 51 234 / 0.1) 100%);
	}

	.gradient-bg-secondary {
		background: linear-gradient(135deg, rgb(16 185 129 / 0.1) 0%, rgb(59 130 246 / 0.1) 100%);
	}

	/* Enhanced Button Styles */
	.btn-modern {
		display: inline-flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.5rem 1rem;
		border-radius: 0.5rem;
		font-weight: 500;
		transition: all 0.2s ease;
		box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
	}

	.btn-modern:hover {
		box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
	}

	.btn-primary-modern {
		background: linear-gradient(to right, rgb(59 130 246), rgb(147 51 234));
		color: white;
		transform: translateY(0);
		transition: all 0.2s ease;
	}

	.btn-primary-modern:hover {
		background: linear-gradient(to right, rgb(37 99 235), rgb(126 34 206));
		transform: translateY(-2px);
	}

	.btn-secondary-modern {
		background-color: var(--vscode-button-secondaryBackground);
		color: var(--vscode-button-secondaryForeground);
	}

	.btn-secondary-modern:hover {
		background-color: var(--vscode-button-hoverBackground);
	}

	/* Enhanced Typography */
	.text-gradient {
		background: linear-gradient(135deg, rgb(59 130 246) 0%, rgb(147 51 234) 100%);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		background-clip: text;
	}

	/* Improved Scrollbars */
	.custom-scrollbar::-webkit-scrollbar {
		width: 8px;
		height: 8px;
	}

	.custom-scrollbar::-webkit-scrollbar-track {
		background: var(--vscode-scrollbarSlider-background);
		border-radius: 4px;
	}

	.custom-scrollbar::-webkit-scrollbar-thumb {
		background: var(--vscode-scrollbarSlider-hoverBackground);
		border-radius: 4px;
		transition: background-color 0.2s ease;
	}

	.custom-scrollbar::-webkit-scrollbar-thumb:hover {
		background: var(--vscode-scrollbarSlider-activeBackground);
	}

	/* Enhanced Focus States */
	.focus-modern:focus-visible {
		outline: none;
		box-shadow: 0 0 0 2px var(--vscode-editor-background), 0 0 0 4px rgb(59 130 246 / 0.5);
	}

	/* Modern Animations */
	@keyframes pulse-glow {
		0%, 100% { opacity: 0.6; }
		50% { opacity: 0.8; }
	}

	@keyframes slide-up {
		from {
			opacity: 0;
			transform: translateY(20px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	@keyframes slide-in-right {
		from {
			opacity: 0;
			transform: translateX(20px);
		}
		to {
			opacity: 1;
			transform: translateX(0);
		}
	}

	@keyframes scale-in {
		from {
			opacity: 0;
			transform: scale(0.95);
		}
		to {
			opacity: 1;
			transform: scale(1);
		}
	}

	@keyframes shimmer {
		0% { transform: translateX(-100%); }
		100% { transform: translateX(100%); }
	}

	.animate-pulse-glow {
		animation: pulse-glow 2s ease-in-out infinite;
	}

	.animate-slide-up {
		animation: slide-up 0.3s ease-out;
	}

	.animate-slide-in-right {
		animation: slide-in-right 0.3s ease-out;
	}

	.animate-scale-in {
		animation: scale-in 0.2s ease-out;
	}

	.animate-shimmer {
		animation: shimmer 2s infinite;
	}

	.interactive:hover {
		@apply bg-vscode-list-hoverBackground text-vscode-list-hoverForeground;
	}

	.interactive:active {
		@apply bg-vscode-list-activeSelectionBackground text-vscode-list-activeSelectionForeground;
	}

	/* Shadow Utilities */
	.shadow-subtle {
		box-shadow: var(--shadow-xs);
	}

	.shadow-soft {
		box-shadow: var(--shadow-sm);
	}

	.shadow-medium {
		box-shadow: var(--shadow-md);
	}

	.shadow-elevated {
		box-shadow: var(--shadow-lg);
	}

	.shadow-high {
		box-shadow: var(--shadow-xl);
	}

	/* Enhanced Button Base Styles */
	.btn-base {
		@apply inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-all duration-150 ease-out focus-ring disabled:pointer-events-none disabled:opacity-50 cursor-pointer select-none;
	}

	.btn-base:active {
		@apply scale-95;
	}

	/* Enhanced Badge Base Styles */
	.badge-base {
		@apply inline-flex items-center font-semibold transition-colors duration-150 ease-out focus-ring;
	}

	/* Enhanced Input Base Styles */
	.input-base {
		@apply flex w-full transition-colors duration-150 ease-out file:border-0 file:bg-transparent file:font-medium placeholder:text-muted-foreground focus-ring disabled:cursor-not-allowed disabled:opacity-50;
	}

	/* State Variants */
	.state-success {
		@apply bg-success text-success-foreground;
	}

	.state-warning {
		@apply bg-warning text-warning-foreground;
	}

	.state-error {
		@apply bg-error text-error-foreground;
	}

	.state-info {
		@apply bg-info text-info-foreground;
	}

	/* Size Variants */
	.size-xs {
		@apply text-2xs px-1.5 py-0.5 h-5;
	}

	.size-sm {
		@apply text-xs px-2 py-1 h-6;
	}

	.size-md {
		@apply text-sm px-3 py-1.5 h-7;
	}

	.size-lg {
		@apply text-base px-4 py-2 h-8;
	}

	.size-xl {
		@apply text-lg px-6 py-3 h-10;
	}
}

/* Form Element Focus States */

textarea:focus {
	outline: 1.5px solid var(--vscode-focusBorder, #007fd4);
}

.focus\:outline-0 {
	outline: 0 !important; /* Allow tailwind to override the `textarea:focus` rule */
}

/**
 * Use vscode native scrollbar styles
 * https://github.com/gitkraken/vscode-gitlens/blob/b1d71d4844523e8b2ef16f9e007068e91f46fd88/src/webviews/apps/home/<USER>
 */

html {
	height: 100%;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

body {
	margin: 0;
	line-height: 1.25;
}

body.scrollable,
.scrollable,
body.code-block-scrollable,
.code-block-scrollable {
	border-color: transparent;
	transition: border-color 0.7s linear;
}

body:hover.scrollable,
body:hover .scrollable,
body:focus-within.scrollable,
body:focus-within .scrollable,
body:hover.code-block-scrollable,
body:hover .code-block-scrollable,
body:focus-within.code-block-scrollable,
body:focus-within .code-block-scrollable {
	border-color: var(--vscode-scrollbarSlider-background);
	transition: none;
}

.scrollable::-webkit-scrollbar-corner {
	background-color: transparent !important;
}

.scrollable::-webkit-scrollbar-thumb {
	background-color: transparent;
	border-color: inherit;
	border-right-style: inset;
	border-right-width: calc(100vw + 100vh);
	border-radius: unset !important;
}

.scrollable::-webkit-scrollbar-thumb:hover {
	border-color: var(--vscode-scrollbarSlider-hoverBackground);
}

.scrollable::-webkit-scrollbar-thumb:active {
	border-color: var(--vscode-scrollbarSlider-activeBackground);
}

/**
 * Fix VSCode ignoring webkit scrollbar modifiers
 * https://github.com/microsoft/vscode/issues/213045
 */
@supports selector(::-webkit-scrollbar) {
	html {
		scrollbar-color: unset;
	}
}

/**
 * The above scrollbar styling uses some transparent background color magic to accomplish its animation. However this doesn't play nicely with SyntaxHighlighter, so we need to set a background color for the code blocks' horizontal scrollbar. This actually has the unintended consequence of always showing the scrollbar which I prefer since it makes it more obvious that there is more content to scroll to.
 */

.code-block-scrollable::-webkit-scrollbar-track {
	background: transparent;
}

.code-block-scrollable::-webkit-scrollbar-thumb {
	background-color: var(--vscode-scrollbarSlider-background);
	border-radius: 5px;
	border: 2px solid transparent;
	background-clip: content-box;
}

.code-block-scrollable::-webkit-scrollbar-thumb:hover {
	background-color: var(--vscode-scrollbarSlider-hoverBackground);
}

.code-block-scrollable::-webkit-scrollbar-thumb:active {
	background-color: var(--vscode-scrollbarSlider-activeBackground);
}

.code-block-scrollable::-webkit-scrollbar-corner {
	background-color: transparent;
}

/**
 * Add a way to hide scrollbars.
 */

.scrollbar-hide {
	-ms-overflow-style: none; /* IE and Edge */
	scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
	display: none; /* Chrome, Safari and Opera */
}

/**
 * Dropdown label
 * https://github.com/microsoft/vscode-webview-ui-toolkit/tree/main/src/dropdown#with-label
 */

.dropdown-container {
	box-sizing: border-box;
	display: flex;
	flex-flow: column nowrap;
	align-items: flex-start;
	justify-content: flex-start;
}

.dropdown-container label {
	display: block;
	color: var(--vscode-foreground);
	cursor: pointer;
	font-size: var(--vscode-font-size);
	line-height: normal;
	margin-bottom: 2px;
}

/* Fix dropdown double scrollbar overflow */

#api-provider > div > ul {
	overflow: unset;
}

/* Fix scrollbar in dropdown */

vscode-dropdown::part(listbox) {
	border-color: var(--vscode-scrollbarSlider-background);
	transition: none;
	scrollbar-color: var(--vscode-scrollbarSlider-background) transparent;
}

/* Faded icon buttons in textfields */
.input-icon-button {
	cursor: pointer;
	opacity: 0.65;
}

.input-icon-button:hover {
	opacity: 1;
}

.input-icon-button.disabled {
	cursor: not-allowed;
	opacity: 0.4;
}

.input-icon-button.disabled:hover {
	opacity: 0.4;
}

/* Context mentions */

.mention-context-textarea-highlight {
	background-color: color-mix(in srgb, var(--vscode-badge-foreground) 30%, transparent);
	border-radius: 3px;
	box-shadow: 0 0 0 0.5px color-mix(in srgb, var(--vscode-badge-foreground) 30%, transparent);
	color: transparent;
}

.mention-context-highlight {
	background-color: color-mix(in srgb, var(--vscode-badge-foreground) 30%, transparent);
	border-radius: 3px;
}

.mention-context-highlight-with-shadow {
	background-color: color-mix(in srgb, var(--vscode-badge-foreground) 30%, transparent);
	border-radius: 3px;
	box-shadow: 0 0 0 0.5px color-mix(in srgb, var(--vscode-badge-foreground) 30%, transparent);
}

/**
 * vscrui Overrides / Hacks
 */

.vscrui-checkbox__listbox > ul {
	max-height: unset !important;
}

.vscrui-checkbox svg {
	min-width: 16px;
	min-height: 16px;
}

/**
 * @shadcn/ui Overrides / Hacks
 */

input[cmdk-input]:focus {
	outline: none;
}

/**
 * Markdown
 */

.custom-markdown > pre {
	background-color: transparent !important;
}

/*
 * Use geometric precision for codicons to avoid blurriness 
 */

.codicon[class*="codicon-"] {
	text-rendering: geometricPrecision !important;
}

/**
 * Custom animations for UI elements
 */

@keyframes slide-in-right {
	from {
		transform: translateX(100%);
	}
	to {
		transform: translateX(0);
	}
}

.animate-slide-in-right {
	animation: slide-in-right 0.3s ease-out;
}

@keyframes fade-in {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

.animate-fade-in {
	animation: fade-in 0.2s ease-out;
}

@keyframes pulse {
	0%,
	100% {
		opacity: 1;
	}
	50% {
		opacity: 0.7;
	}
}

.animate-pulse {
	animation: pulse 1.5s ease-in-out infinite;
}

/* Transition utilities */
.transition-all {
	transition-property: all;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: 150ms;
}

.transition-colors {
	transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: 150ms;
}
