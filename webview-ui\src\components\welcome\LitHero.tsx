import { useState } from "react"

const LitHero = () => {
	const [imagesBaseUri] = useState(() => {
		const w = window as any
		return w.IMAGES_BASE_URI || ""
	})

	return (
		<div className="flex flex-col items-center justify-center py-8 forced-color-adjust-none">
			<div className="relative">
				{/* Glow effect background */}
				<div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-xl scale-150 animate-pulse-glow" />

				{/* Logo container */}
				<div
					style={{
						backgroundColor: "var(--vscode-foreground)",
						WebkitMaskImage: `url('${imagesBaseUri}/lit-logo.svg')`,
						WebkitMaskRepeat: "no-repeat",
						WebkitMaskSize: "contain",
						maskImage: `url('${imagesBaseUri}/lit-logo.svg')`,
						maskRepeat: "no-repeat",
						maskSize: "contain",
					}}
					className="relative mx-auto transition-transform duration-300 hover:scale-110">
					<img src={imagesBaseUri + "/lit-logo.svg"} alt="Lit logo" className="h-12 opacity-0" />
				</div>
			</div>
		</div>
	)
}

export default LitHero
