import { useCallback, useState } from "react"
import knuthShuffle from "knuth-shuffle-seeded"
import { Trans } from "react-i18next"
import { VSCodeButton, VSCodeLink } from "@vscode/webview-ui-toolkit/react"

import type { ProviderSettings } from "@lit-code/types"

import { useExtensionState } from "@src/context/ExtensionStateContext"
import { validateApiConfiguration } from "@src/utils/validate"
import { vscode } from "@src/utils/vscode"
import { useAppTranslation } from "@src/i18n/TranslationContext"
import { getRequestyAuthUrl, getOpenRouterAuthUrl } from "@src/oauth/urls"

import ApiOptions from "../settings/ApiOptions"
import { Tab, TabContent } from "../common/Tab"

import LitHero from "./LitHero"

const WelcomeView = () => {
	const { apiConfiguration, currentApiConfigName, setApiConfiguration, uriScheme, machineId } = useExtensionState()
	const { t } = useAppTranslation()
	const [errorMessage, setErrorMessage] = useState<string | undefined>(undefined)

	// Memoize the setApiConfigurationField function to pass to ApiOptions
	const setApiConfigurationFieldForApiOptions = useCallback(
		<K extends keyof ProviderSettings>(field: K, value: ProviderSettings[K]) => {
			setApiConfiguration({ [field]: value })
		},
		[setApiConfiguration], // setApiConfiguration from context is stable
	)

	const handleSubmit = useCallback(() => {
		const error = apiConfiguration ? validateApiConfiguration(apiConfiguration) : undefined

		if (error) {
			setErrorMessage(error)
			return
		}

		setErrorMessage(undefined)
		vscode.postMessage({ type: "upsertApiConfiguration", text: currentApiConfigName, apiConfiguration })
	}, [apiConfiguration, currentApiConfigName])

	// Using a lazy initializer so it reads once at mount
	const [imagesBaseUri] = useState(() => {
		const w = window as any
		return w.IMAGES_BASE_URI || ""
	})

	return (
		<Tab>
			<TabContent className="flex flex-col gap-8 p-8 max-w-4xl mx-auto">
				<div className="text-center space-y-6">
					<LitHero />
					<div className="space-y-4">
						<h1 className="text-3xl font-bold text-vscode-foreground leading-tight">
							{t("welcome:greeting")}
						</h1>
						<div className="max-w-2xl mx-auto space-y-3">
							<p className="text-lg text-vscode-descriptionForeground leading-relaxed">
								<Trans i18nKey="welcome:introduction" />
							</p>
							<p className="text-base text-vscode-descriptionForeground font-medium">
								<Trans i18nKey="welcome:chooseProvider" />
							</p>
						</div>
					</div>
				</div>

				<div className="space-y-8">
					<div className="space-y-6">
						<h2 className="text-xl font-semibold text-vscode-foreground text-center">
							{t("welcome:startRouter")}
						</h2>

						<div className="grid gap-4 max-w-2xl mx-auto">
							{/* Define the providers */}
							{(() => {
								// Provider card configuration
								const providers = [
									{
										slug: "requesty",
										name: "Requesty",
										description: t("welcome:routers.requesty.description"),
										incentive: t("welcome:routers.requesty.incentive"),
										authUrl: getRequestyAuthUrl(uriScheme),
									},
									{
										slug: "openrouter",
										name: "OpenRouter",
										description: t("welcome:routers.openrouter.description"),
										authUrl: getOpenRouterAuthUrl(uriScheme),
									},
								]

								// Shuffle providers based on machine ID (will be consistent for the same machine)
								const orderedProviders = [...providers]
								knuthShuffle(orderedProviders, (machineId as any) || Date.now())

								// Render the provider cards
								return orderedProviders.map((provider, index) => (
									<a
										key={index}
										href={provider.authUrl}
										className="group relative overflow-hidden border border-vscode-panel-border hover:border-vscode-focusBorder bg-vscode-editor-background hover:bg-vscode-list-hoverBackground rounded-xl p-6 flex items-center gap-4 cursor-pointer transition-all duration-200 no-underline text-inherit shadow-sm hover:shadow-md transform hover:-translate-y-1"
										target="_blank"
										rel="noopener noreferrer">

										{/* Background gradient on hover */}
										<div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />

										<div className="relative w-12 h-12 flex-shrink-0">
											<img
												src={`${imagesBaseUri}/${provider.slug}.png`}
												alt={provider.name}
												className="w-full h-full object-contain rounded-lg"
											/>
										</div>
										<div className="relative flex-1 min-w-0">
											<div className="font-semibold text-vscode-foreground text-lg mb-1">
												{provider.name}
											</div>
											<div className="text-sm text-vscode-descriptionForeground mb-2 leading-relaxed">
												{provider.description}
											</div>
											{provider.incentive && (
												<div className="inline-flex items-center px-2 py-1 bg-green-500/10 text-green-400 text-xs font-medium rounded-md">
													{provider.incentive}
												</div>
											)}
										</div>
										<div className="relative">
											<i className="codicon codicon-arrow-right text-vscode-descriptionForeground group-hover:text-vscode-foreground transition-colors duration-200" />
										</div>
									</a>
								))
							})()}
						</div>
					</div>

					<div className="space-y-6">
						<h2 className="text-xl font-semibold text-vscode-foreground text-center">
							{t("welcome:startCustom")}
						</h2>
						<div className="max-w-2xl mx-auto">
							<ApiOptions
								fromWelcomeView
								apiConfiguration={apiConfiguration || {}}
								uriScheme={uriScheme}
								setApiConfigurationField={setApiConfigurationFieldForApiOptions}
								errorMessage={errorMessage}
								setErrorMessage={setErrorMessage}
							/>
						</div>
					</div>
				</div>
			</TabContent>
			<div className="sticky bottom-0 bg-gradient-to-t from-vscode-sideBar-background via-vscode-sideBar-background to-transparent p-6 border-t border-vscode-panel-border">
				<div className="max-w-4xl mx-auto">
					<div className="flex flex-col gap-4">
						<div className="flex justify-center">
							<VSCodeLink
								href="#"
								onClick={(e) => {
									e.preventDefault()
									vscode.postMessage({ type: "importSettings" })
								}}
								className="text-sm text-vscode-descriptionForeground hover:text-vscode-foreground transition-colors duration-200">
								{t("welcome:importSettings")}
							</VSCodeLink>
						</div>
						<VSCodeButton
							onClick={handleSubmit}
							appearance="primary"
							className="w-full max-w-xs mx-auto py-3 text-base font-medium"
						>
							{t("welcome:start")}
						</VSCodeButton>
						{errorMessage && (
							<div className="text-vscode-errorForeground text-center text-sm bg-vscode-inputValidation-errorBackground border border-vscode-inputValidation-errorBorder rounded-lg p-3">
								{errorMessage}
							</div>
						)}
					</div>
				</div>
			</div>
		</Tab>
	)
}

export default WelcomeView
