import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
	"badge-base rounded-full border transition-colors focus:outline-none focus:ring-2 focus:ring-vscode-focusBorder focus:ring-offset-1",
	{
		variants: {
			variant: {
				default:
					"bg-vscode-badge-background text-vscode-badge-foreground border-transparent hover:bg-vscode-badge-background/80",
				secondary:
					"bg-vscode-button-secondaryBackground text-vscode-button-secondaryForeground border-transparent hover:bg-vscode-button-secondaryBackground/80",
				destructive:
					"bg-error text-error-foreground border-transparent hover:bg-error/80",
				success:
					"bg-success text-success-foreground border-transparent hover:bg-success/80",
				warning:
					"bg-warning text-warning-foreground border-transparent hover:bg-warning/80",
				info:
					"bg-info text-info-foreground border-transparent hover:bg-info/80",
				outline:
					"text-vscode-descriptionForeground border-vscode-input-border bg-transparent hover:bg-vscode-list-hoverBackground hover:text-vscode-list-hoverForeground",
				ghost:
					"text-vscode-foreground border-transparent bg-transparent hover:bg-vscode-list-hoverBackground hover:text-vscode-list-hoverForeground",
			},
			size: {
				xs: "px-1.5 py-0.5 text-2xs h-4",
				sm: "px-2 py-0.5 text-xs h-5",
				default: "px-2.5 py-0.5 text-xs h-6",
				lg: "px-3 py-1 text-sm h-7",
				xl: "px-4 py-1 text-base h-8",
			},
		},
		defaultVariants: {
			variant: "default",
			size: "default",
		},
	},
)

export interface BadgeProps
	extends React.HTMLAttributes<HTMLDivElement>,
		VariantProps<typeof badgeVariants> {
	/**
	 * Whether the badge should be interactive (clickable)
	 */
	interactive?: boolean
}

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
	({ className, variant, size, interactive = false, ...props }, ref) => {
		const baseClasses = cn(
			badgeVariants({ variant, size }),
			interactive && "cursor-pointer hover:scale-105 active:scale-95",
			className
		)

		if (interactive) {
			// Extract only the props that are valid for button elements
			const { children, onClick, onKeyDown, ...divProps } = props
			
			return (
				<button
					className={baseClasses}
					onClick={onClick as unknown as React.MouseEventHandler<HTMLButtonElement>}
					onKeyDown={onKeyDown as unknown as React.KeyboardEventHandler<HTMLButtonElement>}
					type="button"
				>
					{children}
				</button>
			)
		}
		
		return (
			<div
				className={baseClasses}
				ref={ref}
				{...props}
			/>
		)
	}
)
Badge.displayName = "Badge"

export { Badge, badgeVariants }
